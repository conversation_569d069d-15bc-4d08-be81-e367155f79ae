<div class="product-thumbnail" style="margin-bottom: 0; padding-bottom: 0;">
    <a
        class="product-loop__link img-fluid-eq"
        href="<?php echo e($product->url); ?>"
        tabindex="0"
        style="margin-bottom: 0; padding-bottom: 0;"
    >
        <div class="img-fluid-eq__dummy"></div>
        <div class="img-fluid-eq__wrap">
            <img
                class="lazyload product-thumbnail__img"
                data-src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>"
                src="<?php echo e(image_placeholder($product->image, 'small')); ?>"
                alt="<?php echo e($product->name); ?>"
                style="margin-bottom: 0; display: block;"
            >
        </div>
        <span class="ribbons">
            <?php if($product->isOutOfStock()): ?>
                <span class="ribbon out-stock"><?php echo e(__('Out Of Stock')); ?></span>
            <?php else: ?>
                <?php if($product->productLabels->isNotEmpty()): ?>
                    <?php $__currentLoopData = $product->productLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span
                            class="ribbon"
                            <?php if($label->color): ?> style="background-color: <?php echo e($label->color); ?>" <?php endif; ?>
                        ><?php echo e($label->name); ?></span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if($product->front_sale_price !== $product->price): ?>
                        <div
                            class="featured ribbon"
                            dir="ltr"
                        ><?php echo e(get_sale_percentage($product->price, $product->front_sale_price)); ?></div>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>
        </span>
    </a>
    <!-- Action buttons removed -->
</div>
<div class="product-details position-relative" style="padding: 0; margin-top: -5px;">
    <div class="product-content-box" style="padding: 15px;">
        <!-- 1. Product Title (moved to top) -->
        <h3 class="product__title" style="color: #000000 !important; margin-bottom: 8px;">
            <a
                href="<?php echo e($product->url); ?>"
                tabindex="0"
                style="color: #000000 !important; font-weight: 500;"
                onmouseover="this.style.color='#ff6633'"
                onmouseout="this.style.color='#000000'"
            ><?php echo e($product->name); ?></a>
        </h3>

        <!-- 2. Vendor Name (second position) -->
        <?php if(is_plugin_active('marketplace') && $product->store->id): ?>
            <div class="sold-by-meta" style="margin-bottom: 8px;">
                <a
                    href="<?php echo e($product->store->url); ?>"
                    tabindex="0"
                    style="color: #000080 !important; border: 1px solid #000080; border-radius: 4px; padding: 2px 8px; display: inline-block; text-decoration: none; font-size: 12px; font-weight: 500; transition: all 0.3s ease; background-color: rgba(0, 0, 128, 0.05);"
                    onmouseover="this.style.color='#ff6633'; this.style.borderColor='#ff6633'; this.style.backgroundColor='rgba(255, 102, 51, 0.05)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.color='#000080'; this.style.borderColor='#000080'; this.style.backgroundColor='rgba(0, 0, 128, 0.05)'; this.style.boxShadow='none';"
                ><?php echo e($product->store->name); ?></a>
            </div>
        <?php endif; ?>

        <!-- 3. Review Stars (third position) -->
        <?php if(EcommerceHelper::isReviewEnabled()): ?>
            <div style="margin-bottom: 8px;">
                <?php echo Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]); ?>

            </div>
        <?php endif; ?>

        <!-- 4. Price (moved to bottom, without rectangular background) -->
        <?php
            // Check if we're on the search page
            $isSearchPage = request()->has('q') || request()->is('search*');
            // Check if this is a flash sale product
            $isFlashSaleProduct = !empty($isFlashSale);
        ?>

        <div style="margin-top: 8px;">
            <div class="product-price-wrapper" style="color: #ff6633;">
                <?php echo Theme::partial('ecommerce.product-price', compact('product')); ?>

            </div>
        </div>
        <?php if(!empty($isFlashSale)): ?>
            <div class="deal-sold row mt-2">
                <?php if(Botble\Ecommerce\Facades\FlashSale::isShowSaleCountLeft()): ?>
                    <div class="deal-text col-auto">
                        <span class="sold fw-bold">
                            <?php if($product->pivot->quantity > $product->pivot->sold): ?>
                                <span class="text"><?php echo e(__('Sold')); ?>: </span>
                                <span class="value"><?php echo e((int) $product->pivot->sold); ?> /
                                    <?php echo e((int) $product->pivot->quantity); ?></span>
                            <?php else: ?>
                                <span class="text text-danger"><?php echo e(__('Sold out')); ?></span>
                            <?php endif; ?>
                        </span>
                    </div>
                <?php endif; ?>
                <div class="deal-progress col">
                    <div class="progress">
                        <div
                            class="progress-bar"
                            role="progressbar"
                            aria-label="<?php echo e(__('Sold out')); ?>"
                            aria-valuenow="<?php echo e($product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0); ?>"
                            aria-valuemin="0"
                            aria-valuemax="100"
                            style="width: <?php echo e($product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0); ?>%"
                        >
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php
    // Check if we're on the search page
    $isSearchPage = request()->has('q') || request()->is('search*');
?>

<?php if(!$isSearchPage): ?>
<div class="product-bottom-box">
    <?php echo Theme::partial('ecommerce.product-cart-form', compact('product')); ?>

</div>
<?php endif; ?>

<!-- Price now positioned at bottom without rectangular background -->
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/ecommerce/product-item-override.blade.php ENDPATH**/ ?>