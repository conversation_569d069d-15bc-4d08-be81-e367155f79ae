<div class="product-thumbnail" style="margin-bottom: 0; padding-bottom: 0;">
    <a
        class="product-loop__link img-fluid-eq"
        href="{{ $product->url }}"
        tabindex="0"
        style="margin-bottom: 0; padding-bottom: 0;"
    >
        <div class="img-fluid-eq__dummy"></div>
        <div class="img-fluid-eq__wrap">
            <img
                class="lazyload product-thumbnail__img"
                data-src="{{ RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage()) }}"
                src="{{ image_placeholder($product->image, 'small') }}"
                alt="{{ $product->name }}"
                style="margin-bottom: 0; display: block;"
            >
        </div>
        <span class="ribbons">
            @if ($product->isOutOfStock())
                <span class="ribbon out-stock">{{ __('Out Of Stock') }}</span>
            @else
                @if ($product->productLabels->isNotEmpty())
                    @foreach ($product->productLabels as $label)
                        <span
                            class="ribbon"
                            @if ($label->color) style="background-color: {{ $label->color }}" @endif
                        >{{ $label->name }}</span>
                    @endforeach
                @else
                    @if ($product->front_sale_price !== $product->price)
                        <div
                            class="featured ribbon"
                            dir="ltr"
                        >{{ get_sale_percentage($product->price, $product->front_sale_price) }}</div>
                    @endif
                @endif
            @endif
        </span>
    </a>
    <!-- Action buttons removed -->
</div>
<div class="product-details position-relative" style="padding: 0; margin-top: -5px;">
    <div class="product-content-box" style="padding: 15px;">
        <!-- 1. Product Title (moved to top) -->
        <h3 class="product__title" style="color: #000000 !important; margin-bottom: 8px;">
            <a
                href="{{ $product->url }}"
                tabindex="0"
                style="color: #000000 !important; font-weight: 500;"
                onmouseover="this.style.color='#ff6633'"
                onmouseout="this.style.color='#000000'"
            >{{ $product->name }}</a>
        </h3>

        <!-- 2. Vendor Name (second position) -->
        @if (is_plugin_active('marketplace') && $product->store->id)
            <div class="sold-by-meta" style="margin-bottom: 8px;">
                <a
                    href="{{ $product->store->url }}"
                    tabindex="0"
                    style="color: #000080 !important; border: 1px solid #000080; border-radius: 4px; padding: 2px 8px; display: inline-block; text-decoration: none; font-size: 12px; font-weight: 500; transition: all 0.3s ease; background-color: rgba(0, 0, 128, 0.05);"
                    onmouseover="this.style.color='#ff6633'; this.style.borderColor='#ff6633'; this.style.backgroundColor='rgba(255, 102, 51, 0.05)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.color='#000080'; this.style.borderColor='#000080'; this.style.backgroundColor='rgba(0, 0, 128, 0.05)'; this.style.boxShadow='none';"
                >{{ $product->store->name }}</a>
            </div>
        @endif

        <!-- 3. Review Stars (third position) -->
        @if (EcommerceHelper::isReviewEnabled())
            <div style="margin-bottom: 8px;">
                {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
            </div>
        @endif

        <!-- 4. Price (moved to bottom, without rectangular background) -->
        @php
            // Check if we're on the search page
            $isSearchPage = request()->has('q') || request()->is('search*');
            // Check if this is a flash sale product
            $isFlashSaleProduct = !empty($isFlashSale);
        @endphp

        <div style="margin-top: 8px;">
            <div class="product-price-wrapper" style="color: #ff6633;">
                {!! Theme::partial('ecommerce.product-price', compact('product')) !!}
            </div>
        </div>
        @if (!empty($isFlashSale))
            <div class="deal-sold row mt-2">
                @if (Botble\Ecommerce\Facades\FlashSale::isShowSaleCountLeft())
                    <div class="deal-text col-auto">
                        <span class="sold fw-bold">
                            @if ($product->pivot->quantity > $product->pivot->sold)
                                <span class="text">{{ __('Sold') }}: </span>
                                <span class="value">{{ (int) $product->pivot->sold }} /
                                    {{ (int) $product->pivot->quantity }}</span>
                            @else
                                <span class="text text-danger">{{ __('Sold out') }}</span>
                            @endif
                        </span>
                    </div>
                @endif
                <div class="deal-progress col">
                    <div class="progress">
                        <div
                            class="progress-bar"
                            role="progressbar"
                            aria-label="{{ __('Sold out') }}"
                            aria-valuenow="{{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}"
                            aria-valuemin="0"
                            aria-valuemax="100"
                            style="width: {{ $product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0 }}%"
                        >
                        </div>
                    </div>
                </div>
            </div>
        @endisset
    </div>
</div>
@php
    // Check if we're on the search page
    $isSearchPage = request()->has('q') || request()->is('search*');
@endphp

@if (!$isSearchPage)
<div class="product-bottom-box">
    {!! Theme::partial('ecommerce.product-cart-form', compact('product')) !!}
</div>
@endif

<!-- Price now positioned at bottom without rectangular background -->
