/* List view product layout fixes */

/* Remove background from price in list view and ensure orange color */
.shop-products-listing__list .product-inner .product-details .product-price-wrapper {
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price,
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-sale,
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-original {
    background: none !important;
    padding: 0 !important;
}

.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price .amount,
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price span:not(del span),
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-sale .amount,
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-sale span:not(del span),
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-original .amount,
.shop-products-listing__list .product-inner .product-details .product-price-wrapper .product-price-original span:not(del span) {
    color: #ff6633 !important;
    background: none !important;
    padding: 0 !important;
}

/* Ensure proper spacing between elements in list view */
.shop-products-listing__list .product-inner .product-details .product-content-box .product__title {
    margin-bottom: 8px;
}

.shop-products-listing__list .product-inner .product-details .product-content-box .sold-by-meta {
    margin-bottom: 8px;
}

.shop-products-listing__list .product-inner .product-details .product-content-box .star-rating-wrapper {
    margin-bottom: 8px;
}

/* Ensure price is at the bottom with proper spacing */
.shop-products-listing__list .product-inner .product-details .product-content-box .product-price-wrapper {
    margin-top: 8px;
}

/* Make product images occupy full width of product cards */
.product-inner {
    padding: 0 !important;
}

.product-inner .product-thumbnail {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
}

.product-inner .product-thumbnail .img-fluid-eq {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.product-inner .product-thumbnail .img-fluid-eq .img-fluid-eq__wrap {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.product-inner .product-thumbnail .img-fluid-eq .img-fluid-eq__wrap img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Restore padding only for product details content */
.product-inner .product-details {
    padding: 15px 25px 20px !important;
}

/* Restore padding for product bottom box */
.product-inner .product-bottom-box {
    padding: 17px 25px 25px !important;
}
