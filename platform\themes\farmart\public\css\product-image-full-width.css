/* Product Image Full Width Fixes */

/* Make product images occupy full width of product cards */
.product-inner {
    padding: 0 !important;
    overflow: hidden;
}

.product-inner .product-thumbnail {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    position: relative;
}

.product-inner .product-thumbnail .img-fluid-eq {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block;
}

.product-inner .product-thumbnail .img-fluid-eq .img-fluid-eq__dummy {
    margin-top: 100% !important;
    width: 100% !important;
}

.product-inner .product-thumbnail .img-fluid-eq .img-fluid-eq__wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

.product-inner .product-thumbnail .img-fluid-eq .img-fluid-eq__wrap img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block;
}

/* Restore padding only for product details content */
.product-inner .product-details {
    padding: 15px 25px 20px !important;
}

/* Restore padding for product bottom box */
.product-inner .product-bottom-box {
    padding: 17px 25px 25px !important;
}

/* Ensure ribbons/labels are positioned correctly */
.product-inner .product-thumbnail .ribbons {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

/* Ensure product loop buttons are positioned correctly */
.product-inner .product-thumbnail .product-loop__buttons {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

/* Grid view specific adjustments */
.shop-products-listing__grid .product-inner .product-thumbnail {
    margin-bottom: 0 !important;
}

/* List view specific adjustments */
.shop-products-listing__list .product-inner {
    display: flex;
    align-items: stretch;
}

.shop-products-listing__list .product-inner .product-thumbnail {
    flex-basis: 21%;
    width: 21%;
    margin-bottom: 0 !important;
}

.shop-products-listing__list .product-inner .product-details {
    flex: 1;
    padding-left: 30px !important;
}

/* Mobile responsive adjustments */
@media (max-width: 992px) {
    .product-inner .product-details {
        padding: 10px 15px 15px !important;
    }
    
    .shop-products-listing__list .product-inner .product-details {
        padding-left: 20px !important;
    }
}
