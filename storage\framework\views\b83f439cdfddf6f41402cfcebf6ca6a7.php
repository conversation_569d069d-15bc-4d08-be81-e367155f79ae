<!-- AliExpress-style cart sidebar for desktop (993px+) -->
<style>
    /* Hide on mobile */
    @media (max-width: 992px) {
        .aliexpress-style-cart {
            display: none !important;
        }
    }

    /* Desktop AliExpress-style layout (993px and above) */
    @media (min-width: 993px) {
        /* AliExpress-style cart container */
        .aliexpress-style-cart {
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }

        /* Seller info section */
        .seller-info {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .seller-info .label {
            color: #666;
            font-size: 13px;
            margin-bottom: 3px;
        }

        .seller-info .value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .seller-info .value a {
            color: #1890ff;
            text-decoration: none;
        }

        .seller-info .value a:hover {
            text-decoration: underline;
        }

        /* Shipping info section */
        .shipping-info {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .shipping-info .label {
            color: #666;
            font-size: 13px;
            margin-bottom: 3px;
        }

        .shipping-info .value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .shipping-location-selector {
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: color 0.2s;
        }

        .shipping-location-selector:hover {
            color: #1890ff;
        }

        .shipping-info .location-icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
            flex-shrink: 0;
        }

        .dropdown-icon {
            width: 12px;
            height: 12px;
            margin-left: 4px;
            flex-shrink: 0;
        }

        /* Commitment section */
        .aliexpress-commitment {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .commitment-title {
            color: #333;
            font-weight: 600;
            font-size: 13px;
            margin-bottom: 6px;
        }

        .commitment-item {
            display: flex;
            align-items: center;
            padding: 6px 0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .commitment-item:hover {
            background-color: #f9f9f9;
        }

        .commitment-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #52c41a;
            flex-shrink: 0;
        }

        .commitment-text {
            flex: 1;
            color: #333;
            font-size: 13px;
            line-height: 1.3;
        }

        .commitment-detail {
            color: #666;
            font-size: 11px;
            margin-top: 1px;
        }

        .commitment-arrow {
            width: 14px;
            height: 14px;
            color: #999;
            flex-shrink: 0;
        }

        /* Quantity section */
        .quantity-section {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .quantity-label {
            color: #333;
            font-size: 13px;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quantity-btn {
            width: 28px;
            height: 28px;
            border: 1px solid #d9d9d9;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .quantity-btn:hover {
            border-color: #ff4747;
            color: #ff4747;
        }

        .quantity-btn:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .quantity-display {
            width: 50px;
            height: 28px;
            text-align: center;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            font-size: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            line-height: 1;
            background: #fff;
            color: #333;
        }

        .stock-info {
            color: #666;
            font-size: 11px;
            margin-top: 3px;
        }

        /* Action buttons section */
        .action-buttons {
            padding: 14px 0 10px;
        }

        .buy-now-btn {
            width: 100%;
            height: 40px;
            background: #ff4747;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 8px;
            transition: background-color 0.2s;
        }

        .buy-now-btn:hover {
            background: #e63946;
        }

        .buy-now-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .add-to-cart-btn {
            width: 100%;
            height: 40px;
            background: #fff;
            color: #ff4747;
            border: 1px solid #ff4747;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-to-cart-btn:hover {
            background: #ff4747;
            color: #fff;
        }

        .add-to-cart-btn:disabled {
            border-color: #ccc;
            color: #ccc;
            cursor: not-allowed;
        }

        /* Location Selector Popup */
        .location-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }

        /* Hide location selector on mobile */
        @media (max-width: 992px) {
            .location-popup-overlay,
            .location-popup,
            #location-popup-overlay,
            #location-popup,
            .shipping-location-selector,
            #shipping-location-btn,
            .shipping-info {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }
        }

        .location-popup {
            position: absolute;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            width: 320px;
            max-height: 400px;
            overflow: hidden;
        }

        .location-popup-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .location-popup-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .location-popup-close {
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: #666;
            background: none;
            border: none;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .location-popup-close:hover {
            color: #333;
        }

        .location-popup-content {
            padding: 16px;
            max-height: 320px;
            overflow-y: auto;
        }

        .location-step {
            display: none;
        }

        .location-step.active {
            display: block;
        }

        .location-step-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 12px;
        }

        .location-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .location-item {
            padding: 10px 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s;
            font-size: 14px;
            color: #333;
        }

        .location-item:hover {
            background-color: #f5f5f5;
        }

        .location-item.selected {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .location-loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .location-back-btn {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 12px;
            padding: 4px 0;
        }

        .location-back-btn:hover {
            text-decoration: underline;
        }

        .location-back-icon {
            width: 16px;
            height: 16px;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .location-popup {
                width: 90vw;
                max-width: 320px;
                left: 5vw !important;
                top: 50% !important;
                transform: translateY(-50%);
            }
        }


    }
</style>

<?php
    // Get store information
    $store = $product->store ?? null;
    $storeName = $store ? $store->name : get_ecommerce_setting('store_name', config('app.name', 'Store'));
    $storeUrl = $store && $store->slug ? route('public.store', $store->slug) : '#';

    // Get shipping location from session or default to Dhaka
    $sessionData = session('checkout_address_data', []);
    $selectedState = $sessionData['state'] ?? null;
    $selectedCity = $sessionData['city'] ?? null;

    // Default shipping location
    $defaultLocation = 'Dhaka';
    $shippingLocation = $defaultLocation;

    // If we have session data, try to get the location name
    if ($selectedCity && $selectedState) {
        try {
            $cityModel = \Botble\Location\Models\City::find($selectedCity);
            $stateModel = \Botble\Location\Models\State::find($selectedState);
            if ($cityModel && $stateModel) {
                $shippingLocation = $cityModel->name . ', ' . $stateModel->name;
            } elseif ($cityModel) {
                $shippingLocation = $cityModel->name;
            } elseif ($stateModel) {
                $shippingLocation = $stateModel->name;
            }
        } catch (Exception $e) {
            $shippingLocation = $defaultLocation;
        }
    }

    // Calculate delivery date (add 7-14 days from now)
    $deliveryDays = rand(7, 14);
    $deliveryDate = now()->addDays($deliveryDays)->format('M. j');

    // Get actual stock quantity
    $stockQuantity = $product->with_storehouse_management ? $product->quantity : 999;
?>

<div class="aliexpress-style-cart">
    <!-- Seller Information -->
    <div class="seller-info">
        <div class="label"><?php echo e(__('Sold by')); ?></div>
        <div class="value">
            <?php if($store): ?>
                <a href="<?php echo e($storeUrl); ?>" target="_blank"><?php echo e($storeName); ?>(Trader)</a>
            <?php else: ?>
                <a href="#" onclick="return false;"><?php echo e($storeName); ?>(Trader)</a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Shipping Information -->
    <div class="shipping-info">
        <div class="label"><?php echo e(__('Ship to')); ?></div>
        <div class="value shipping-location-selector" id="shipping-location-btn">
            <svg class="location-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
            <span id="selected-location"><?php echo e($shippingLocation); ?></span>
            <svg class="dropdown-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>

    <!-- Store Commitment -->
    <div class="aliexpress-commitment">
        <div class="commitment-title"><?php echo e(__('Bulbulee Commitment')); ?></div>

        <div class="commitment-item">
            <svg class="commitment-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <div class="commitment-text">
                <div><?php echo e(__('Free shipping')); ?></div>
                <div class="commitment-detail"><?php echo e(__('On Purchase over ৳5000')); ?></div>
            </div>
            <svg class="commitment-arrow" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
        </div>

        <div class="commitment-item">
            <svg class="commitment-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
            </svg>
            <div class="commitment-text">
                <div><?php echo e(__('7 Days Easy Return')); ?></div>
            </div>
            <svg class="commitment-arrow" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
        </div>

        <div class="commitment-item">
            <svg class="commitment-icon" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <div class="commitment-text">
                <div><?php echo e(__('Security & Privacy')); ?></div>
                <div class="commitment-detail"><?php echo e(__('Safe payments: We do not share your personal details')); ?></div>
                <div class="commitment-detail"><?php echo e(__('Secure personal details: We protect your privacy and secure personal details')); ?></div>
            </div>
            <svg class="commitment-arrow" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>

    <!-- Quantity Section -->
    <div class="quantity-section">
        <div class="quantity-label"><?php echo e(__('Quantity')); ?></div>
        <div class="quantity-controls">
            <button type="button" class="quantity-btn" id="decrease-qty">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                </svg>
            </button>
            <div class="quantity-display" id="qty-display"><?php echo e($product->minimum_order_quantity ?: 1); ?></div>
            <button type="button" class="quantity-btn" id="increase-qty">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                </svg>
            </button>
        </div>
        <div class="stock-info"><?php echo e(number_format($stockQuantity)); ?> <?php echo e(__('available')); ?></div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <?php if(EcommerceHelper::isQuickBuyButtonEnabled()): ?>
            <button
                class="buy-now-btn <?php if($product->isOutOfStock()): ?> disabled <?php endif; ?>"
                onclick="handleBuyNow()"
                <?php if($product->isOutOfStock()): ?> disabled <?php endif; ?>
            >
                <?php echo e(__('Buy now')); ?>

            </button>
        <?php endif; ?>

        <?php if(EcommerceHelper::isCartEnabled()): ?>
            <button
                class="add-to-cart-btn <?php if($product->isOutOfStock()): ?> disabled <?php endif; ?>"
                onclick="handleAddToCart()"
                <?php if($product->isOutOfStock()): ?> disabled <?php endif; ?>
            >
                <?php echo e(__('Add to cart')); ?>

            </button>
        <?php endif; ?>
    </div>


</div>

<!-- Location Selector Popup -->
<div class="location-popup-overlay" id="location-popup-overlay">
    <div class="location-popup" id="location-popup">
        <div class="location-popup-header">
            <div class="location-popup-title"><?php echo e(__('Select Location')); ?></div>
            <button class="location-popup-close" id="location-popup-close">
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <div class="location-popup-content">
            <!-- Step 1: Select Division -->
            <div class="location-step active" id="step-division">
                <div class="location-step-title"><?php echo e(__('Select Division')); ?></div>
                <div class="location-loading" id="division-loading"><?php echo e(__('Loading divisions...')); ?></div>
                <ul class="location-list" id="division-list" style="display: none;"></ul>
            </div>

            <!-- Step 2: Select District -->
            <div class="location-step" id="step-district">
                <div class="location-back-btn" id="back-to-division">
                    <svg class="location-back-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(__('Back')); ?>

                </div>
                <div class="location-step-title"><?php echo e(__('Select District')); ?></div>
                <div class="location-loading" id="district-loading"><?php echo e(__('Loading districts...')); ?></div>
                <ul class="location-list" id="district-list" style="display: none;"></ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for AliExpress-style quantity controls -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const decreaseBtn = document.getElementById('decrease-qty');
    const increaseBtn = document.getElementById('increase-qty');
    const qtyDisplay = document.getElementById('qty-display');

    if (decreaseBtn && increaseBtn && qtyDisplay) {
        const minQty = <?php echo e($product->minimum_order_quantity ?: 1); ?>;
        const maxQty = <?php echo e($stockQuantity); ?>;
        let currentQty = minQty;

        // Decrease quantity
        decreaseBtn.addEventListener('click', function() {
            if (currentQty > minQty) {
                currentQty--;
                qtyDisplay.textContent = currentQty;
                // Also update the original quantity input if it exists
                const originalQtyInput = document.querySelector('.cart-form input[name="qty"]');
                if (originalQtyInput) {
                    originalQtyInput.value = currentQty;
                }
            }
        });

        // Increase quantity
        increaseBtn.addEventListener('click', function() {
            if (currentQty < maxQty) {
                currentQty++;
                qtyDisplay.textContent = currentQty;
                // Also update the original quantity input if it exists
                const originalQtyInput = document.querySelector('.cart-form input[name="qty"]');
                if (originalQtyInput) {
                    originalQtyInput.value = currentQty;
                }
            }
        });

        // Sync with original quantity input changes
        const originalQtyInput = document.querySelector('.cart-form input[name="qty"]');
        if (originalQtyInput) {
            originalQtyInput.addEventListener('change', function() {
                currentQty = parseInt(this.value) || minQty;
                if (currentQty < minQty) currentQty = minQty;
                if (currentQty > maxQty) currentQty = maxQty;
                qtyDisplay.textContent = currentQty;
            });
        }
    }
});

// Handle Buy Now button click
function handleBuyNow() {
    const form = document.querySelector('.cart-form');
    const qtyDisplay = document.getElementById('qty-display');

    if (form && qtyDisplay) {
        // Update the form quantity
        const formQtyInput = form.querySelector('input[name="qty"]');
        if (formQtyInput) {
            formQtyInput.value = qtyDisplay.textContent;
        }

        // Create and add checkout input
        const checkoutInput = document.createElement('input');
        checkoutInput.type = 'hidden';
        checkoutInput.name = 'checkout';
        checkoutInput.value = '1';
        form.appendChild(checkoutInput);

        // Submit the form
        form.submit();
    }
}

// Handle Add to Cart button click
function handleAddToCart() {
    const form = document.querySelector('.cart-form');
    const qtyDisplay = document.getElementById('qty-display');

    if (form && qtyDisplay) {
        // Update the form quantity
        const formQtyInput = form.querySelector('input[name="qty"]');
        if (formQtyInput) {
            formQtyInput.value = qtyDisplay.textContent;
        }

        // Create and add add_to_cart input
        const addToCartInput = document.createElement('input');
        addToCartInput.type = 'hidden';
        addToCartInput.name = 'add_to_cart';
        addToCartInput.value = '1';
        form.appendChild(addToCartInput);

        // Submit the form
        form.submit();
    }
}

// Location Selector Functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Location selector script loaded');

    // Check if mobile view (992px breakpoint)
    function isMobileView() {
        return window.innerWidth <= 992;
    }

    // Exit early if mobile view
    if (isMobileView()) {
        console.log('Mobile view detected - location selector disabled');
        return;
    }

    const locationBtn = document.getElementById('shipping-location-btn');
    const overlay = document.getElementById('location-popup-overlay');
    const popup = document.getElementById('location-popup');
    const closeBtn = document.getElementById('location-popup-close');
    const backBtn = document.getElementById('back-to-division');
    const selectedLocationSpan = document.getElementById('selected-location');

    console.log('Location elements found:', {
        locationBtn: !!locationBtn,
        overlay: !!overlay,
        popup: !!popup,
        closeBtn: !!closeBtn,
        backBtn: !!backBtn,
        selectedLocationSpan: !!selectedLocationSpan
    });

    let selectedDivision = null;
    let selectedDistrict = null;
    let bangladeshCountryId = null;

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    console.log('CSRF token found:', !!csrfToken);

    // Open popup
    locationBtn.addEventListener('click', function() {
        overlay.style.display = 'block';
        positionPopup();
        loadDivisions();
    });

    // Close popup
    closeBtn.addEventListener('click', closePopup);
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closePopup();
        }
    });

    // Back to division step
    backBtn.addEventListener('click', function() {
        showStep('step-division');
    });

    function closePopup() {
        overlay.style.display = 'none';
        showStep('step-division');
    }

    function positionPopup() {
        // On mobile, center the popup
        if (window.innerWidth <= 768) {
            popup.style.top = '50%';
            popup.style.left = '5vw';
            popup.style.transform = 'translateY(-50%)';
            return;
        }

        const rect = locationBtn.getBoundingClientRect();

        // Position popup near the shipping location button
        let top = rect.bottom + 10;
        let left = rect.left;

        // Adjust if popup goes off screen
        if (left + 320 > window.innerWidth) {
            left = window.innerWidth - 320 - 20;
        }
        if (top + 400 > window.innerHeight) {
            top = rect.top - 400 - 10;
        }

        popup.style.top = top + 'px';
        popup.style.left = left + 'px';
        popup.style.transform = 'none';
    }

    function showStep(stepId) {
        document.querySelectorAll('.location-step').forEach(step => {
            step.classList.remove('active');
        });
        document.getElementById(stepId).classList.add('active');
    }

    function loadDivisions() {
        const loading = document.getElementById('division-loading');
        const list = document.getElementById('division-list');

        loading.style.display = 'block';
        list.style.display = 'none';
        list.innerHTML = '';

        // Check if location plugin is active first
        console.log('Checking if location plugin routes are available...');

        // Try Bangladesh (country ID 1) first since it's the default country
        console.log('Loading states for Bangladesh (country ID 1)...');
        console.log('Trying URL: /ajax/states-by-country?country_id=1');

        fetch('/ajax/states-by-country?country_id=1', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
            }
        })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                console.log('Response URL:', response.url);
                if (!response.ok) {
                    // Let's see what the actual response is
                    return response.text().then(text => {
                        console.log('Error response body:', text);
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('Bangladesh states response:', data);
                console.log('Number of states returned:', data.data ? data.data.length : 0);
                if (data.data && data.data.length > 1) { // More than just "Select state"
                    bangladeshCountryId = 1;
                    const validStates = data.data.filter(state => state.id !== 0);
                    console.log('Valid states after filtering:', validStates);
                    displayDivisions(validStates);
                } else {
                    // Fallback: try loading all states
                    console.log('No states for country ID 1, trying all states...');
                    return fetch('/ajax/states-by-country', {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
                        }
                    });
                }
            })
            .then(response => {
                if (response) {
                    return response.json();
                }
            })
            .then(data => {
                if (data && data.data && data.data.length > 0) {
                    bangladeshCountryId = null;
                    const validStates = data.data.filter(state => state.id !== 0);
                    if (validStates.length > 0) {
                        displayDivisions(validStates);
                    } else {
                        throw new Error('No valid states found');
                    }
                }
            })
            .catch(error => {
                console.error('Error loading divisions:', error);
                loading.textContent = 'Error loading divisions. Please check if states are published in admin panel.';
            });

        function displayDivisions(divisions) {
            loading.style.display = 'none';
            list.style.display = 'block';

            divisions.forEach(division => {
                const li = document.createElement('li');
                li.className = 'location-item';
                li.textContent = division.name;
                li.dataset.id = division.id;
                li.dataset.name = division.name;

                li.addEventListener('click', function() {
                    selectedDivision = {
                        id: this.dataset.id,
                        name: this.dataset.name
                    };
                    loadDistricts(selectedDivision.id);
                });

                list.appendChild(li);
            });
        }
    }

    function loadDistricts(divisionId) {
        showStep('step-district');

        const loading = document.getElementById('district-loading');
        const list = document.getElementById('district-list');

        loading.style.display = 'block';
        list.style.display = 'none';
        list.innerHTML = '';

        // Build URL with proper parameters
        let url = `/ajax/cities-by-state?state_id=${divisionId}`;
        if (bangladeshCountryId) {
            url += `&country_id=${bangladeshCountryId}`;
        }

        console.log('Loading districts for division ID:', divisionId);
        console.log('Fetching URL:', url);

        fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
            }
        })
            .then(response => {
                console.log('Districts response status:', response.status);
                console.log('Districts response URL:', response.url);
                if (!response.ok) {
                    return response.text().then(text => {
                        console.log('Districts error response body:', text);
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                console.log('Districts response data:', data);
                loading.style.display = 'none';
                list.style.display = 'block';

                if (data.data && data.data.length > 0) {
                    console.log('Number of districts found:', data.data.length);
                    data.data.forEach(district => {
                        if (district.id !== null && district.id !== 0) { // Skip the "Select city" option
                            console.log('Adding district:', district.name, 'ID:', district.id);
                            const li = document.createElement('li');
                            li.className = 'location-item';
                            li.textContent = district.name;
                            li.dataset.id = district.id;
                            li.dataset.name = district.name;

                            li.addEventListener('click', function() {
                                selectedDistrict = {
                                    id: this.dataset.id,
                                    name: this.dataset.name
                                };
                                console.log('Selected district:', selectedDistrict);
                                updateLocation();
                            });

                            list.appendChild(li);
                        }
                    });
                } else {
                    console.log('No districts data found');
                    loading.textContent = 'No districts found for this division';
                }
            })
            .catch(error => {
                console.error('Error loading districts:', error);
                loading.style.display = 'none';
                loading.textContent = 'Error loading districts';
            });
    }

    function updateLocation() {
        // Update the display
        selectedLocationSpan.textContent = `${selectedDistrict.name}, ${selectedDivision.name}`;

        // Update session data via AJAX
        const formData = new FormData();
        formData.append('state', selectedDivision.id);
        formData.append('city', selectedDistrict.id);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '');

        fetch('/ajax/update-checkout-address', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Location updated successfully:', data);
            console.log('Selected state ID:', selectedDivision.id);
            console.log('Selected city ID:', selectedDistrict.id);
        })
        .catch(error => {
            console.error('Error updating location:', error);
        });

        closePopup();
    }
});
</script>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/ecommerce/aliexpress-style-cart.blade.php ENDPATH**/ ?>